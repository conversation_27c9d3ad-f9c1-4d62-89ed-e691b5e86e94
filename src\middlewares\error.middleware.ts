import { Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';
import { LoggedRequest } from '@/types/logger.types';

// 404 handler
export const notFoundHandler = (req: LoggedRequest, res: Response, _next: NextFunction): void => {
  const requestId = req.requestId;

  logger.warn(`404 - Route not found: ${req.method} ${req.originalUrl}`, {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
  }, requestId);

  res.status(404).json({
    success: false,
    message: `Can't find ${req.originalUrl} on this server!`,
    requestId,
  });
};

// Global error handling middleware
export const globalErrorHandler = (
  err: any,
  req: LoggedRequest,
  res: Response,
  _next: NextFunction
): void => {
  const statusCode = err.statusCode || 500;
  const message = err.message || 'Something went wrong!';
  const requestId = req.requestId;

  // Log the error with full details
  logger.error(`Global Error Handler - ${message}`, {
    statusCode,
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.headers['user-agent'],
    stack: err.stack,
    name: err.name,
  }, requestId);

  res.status(statusCode).json({
    success: false,
    message,
    requestId,
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack }),
  });
};
