import { Router } from 'express';
import { exampleController } from '@/controllers/example.controller';

const router = Router();

// Example routes to demonstrate logger functionality
router.get('/info', exampleController.getInfo);
router.get('/detailed', exampleController.getDetailedExample);
router.get('/error', exampleController.getErrorExample);
router.get('/warning', exampleController.getWarningExample);
router.post('/post-example', exampleController.postExample);

export default router;
