import { Request, Response } from 'express';
import { logger } from '@/utils/logger';
import { ResponseUtil } from '@/utils/response';
import { LoggedRequest, LoggedResponse } from '@/types/logger.types';

// Example controller to demonstrate logger usage
export const exampleController = {
  // Basic info logging example
  getInfo: (req: LoggedRequest, res: LoggedResponse) => {
    const requestId = req.requestId!;
    
    logger.info('Getting application info', { 
      endpoint: '/info',
      method: req.method 
    }, requestId);
    
    ResponseUtil.success(res, 'Application info retrieved successfully', {
      data: {
        name: 'RiteFit.AI Backend',
        version: '1.0.0',
        environment: process.env.NODE_ENV,
        timestamp: new Date().toISOString(),
      }
    });
  },

  // Example with detailed logging (when ?detailed_logging=true)
  getDetailedExample: (req: LoggedRequest, res: LoggedResponse) => {
    const requestId = req.requestId!;
    
    logger.info('Processing detailed example request', {
      queryParams: req.query,
      hasDetailedLogging: req.logConfig?.enableDetailedLogging,
    }, requestId);
    
    // Simulate some processing
    const processingData = {
      step1: 'Data validation',
      step2: 'Business logic processing',
      step3: 'Response preparation',
    };
    
    logger.debug('Processing steps completed', processingData, requestId);
    
    ResponseUtil.success(res, 'Detailed example processed successfully', {
      data: {
        processed: true,
        steps: processingData,
        requestId,
      }
    });
  },

  // Example error handling
  getErrorExample: (req: LoggedRequest, res: LoggedResponse) => {
    const requestId = req.requestId!;
    
    try {
      logger.info('Processing error example request', {}, requestId);
      
      // Simulate an error
      throw new Error('This is a simulated error for demonstration');
      
    } catch (error) {
      logger.error('Error in example controller', error as Error, requestId);
      
      ResponseUtil.error(res, 'An error occurred while processing the request', 500, 
        process.env.NODE_ENV === 'development' ? (error as Error).stack : undefined
      );
    }
  },

  // Example with warning
  getWarningExample: (req: LoggedRequest, res: LoggedResponse) => {
    const requestId = req.requestId!;
    
    logger.warn('Warning example endpoint accessed', {
      userAgent: req.headers['user-agent'],
      ip: req.ip,
    }, requestId);
    
    ResponseUtil.success(res, 'Warning example completed', {
      data: {
        warning: 'This endpoint is deprecated and will be removed in future versions',
        requestId,
      }
    });
  },

  // Example POST request with body logging
  postExample: (req: LoggedRequest, res: LoggedResponse) => {
    const requestId = req.requestId!;
    
    logger.info('Processing POST example', {
      bodySize: JSON.stringify(req.body).length,
      contentType: req.headers['content-type'],
    }, requestId);
    
    // Validate request body
    if (!req.body || Object.keys(req.body).length === 0) {
      logger.warn('Empty request body received', {}, requestId);
      return ResponseUtil.error(res, 'Request body is required', 400);
    }
    
    logger.debug('Request body validated successfully', {
      fields: Object.keys(req.body),
    }, requestId);
    
    ResponseUtil.success(res, 'POST request processed successfully', {
      data: {
        received: req.body,
        processed: true,
        requestId,
      }
    });
  },
};
