import { logger } from '@/utils/logger';
import { LoggedResponse } from '@/types/logger.types';

export const ResponseUtil = {
  success: (res: LoggedResponse, message: string, result?: any) => {
    const requestId = res.requestId;

    // Log success response
    if (requestId) {
      logger.logSuccess(message, requestId, {
        statusCode: 200,
        hasData: !!result?.data,
        hasPagination: !!result?.pagination,
      });
    }

    res.status(200).json({
      success: true,
      message,
      data: result?.data,
      pagination: result?.pagination,
      requestId,
    });
  },

  error: (res: LoggedResponse, message: string, statusCode: number = 500, stack?: string) => {
    const requestId = res.requestId;

    // Log error response
    if (requestId) {
      logger.logError(message, requestId, stack ? new Error(stack) : undefined, {
        statusCode,
      });
    }

    res.status(statusCode).json({
      success: false,
      message,
      requestId,
      ...(process.env.NODE_ENV === 'development' ? { stack } : {}),
    });
  },
};