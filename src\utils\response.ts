import { Response } from 'express';

export const ResponseUtil = {
  success: (res: Response, message: string, result?: any) => {
    res.status(200).json({
      success: true,
      message,
      data: result?.data,
      paggination: result?.pagination,
    });
  },
  error: (res: Response, message: string, statusCode: number = 500 , stack?: string) => {
    res.status(statusCode).json({
      success: false,
      message,
      ...(process.env.NODE_ENV === 'development' ? { stack } : {}),
    });
  },
};