import { Request, Response, NextFunction } from 'express';
import { logger, generateRequestId } from '@/utils/logger';
import { 
  LoggedRequest, 
  LoggedResponse, 
  RequestLogConfig, 
  ApiRequestLog, 
  ApiResponseLog 
} from '@/types/logger.types';

// Default configuration for request logging
const defaultLogConfig: RequestLogConfig = {
  enableDetailedLogging: false,
  logHeaders: true,
  logBody: true,
  logQuery: true,
  logParams: true,
  excludeHeaders: ['authorization', 'cookie', 'x-api-key'],
  excludeBodyFields: ['password', 'token', 'secret'],
};

// Sanitize sensitive data from headers
const sanitizeHeaders = (headers: Record<string, any>, excludeHeaders: string[] = []): Record<string, any> => {
  const sanitized = { ...headers };
  
  excludeHeaders.forEach(header => {
    const lowerHeader = header.toLowerCase();
    Object.keys(sanitized).forEach(key => {
      if (key.toLowerCase() === lowerHeader) {
        sanitized[key] = '[REDACTED]';
      }
    });
  });
  
  return sanitized;
};

// Sanitize sensitive data from body
const sanitizeBody = (body: any, excludeFields: string[] = []): any => {
  if (!body || typeof body !== 'object') {
    return body;
  }
  
  const sanitized = { ...body };
  
  excludeFields.forEach(field => {
    if (sanitized[field] !== undefined) {
      sanitized[field] = '[REDACTED]';
    }
  });
  
  return sanitized;
};

// Get client IP address
const getClientIp = (req: Request): string => {
  return (
    (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    'unknown'
  );
};

// Request logging middleware
export const requestLogger = (req: LoggedRequest, res: LoggedResponse, next: NextFunction): void => {
  // Generate unique request ID
  const requestId = generateRequestId();
  req.requestId = requestId;
  res.requestId = requestId;
  
  // Record start time
  req.startTime = Date.now();
  
  // Get logging configuration from query parameters or use defaults
  const enableDetailedLogging = req.query.detailed_logging === 'true' || req.query.debug === 'true';
  
  req.logConfig = {
    ...defaultLogConfig,
    enableDetailedLogging,
  };
  
  // Log request if detailed logging is enabled
  if (req.logConfig.enableDetailedLogging) {
    const requestData: ApiRequestLog = {
      requestId,
      method: req.method,
      url: req.originalUrl || req.url,
      timestamp: new Date().toISOString(),
      userAgent: req.headers['user-agent'],
      ip: getClientIp(req),
    };
    
    // Add headers if enabled
    if (req.logConfig.logHeaders) {
      requestData.headers = sanitizeHeaders(req.headers, req.logConfig.excludeHeaders);
    }
    
    // Add body if enabled and present
    if (req.logConfig.logBody && req.body) {
      requestData.body = sanitizeBody(req.body, req.logConfig.excludeBodyFields);
    }
    
    // Add query parameters if enabled
    if (req.logConfig.logQuery && Object.keys(req.query).length > 0) {
      requestData.query = req.query;
    }
    
    // Add route parameters if enabled
    if (req.logConfig.logParams && Object.keys(req.params).length > 0) {
      requestData.params = req.params;
    }
    
    logger.logApiRequest(requestData);
  } else {
    // Basic request logging
    logger.info(`${req.method} ${req.originalUrl || req.url}`, {
      ip: getClientIp(req),
      userAgent: req.headers['user-agent'],
    }, requestId);
  }
  
  // Override res.json to capture response
  const originalJson = res.json;
  res.json = function(body: any) {
    const responseTime = Date.now() - (req.startTime || Date.now());
    
    const responseData: ApiResponseLog = {
      requestId,
      statusCode: res.statusCode,
      responseTime,
      success: res.statusCode < 400,
      timestamp: new Date().toISOString(),
    };
    
    // Add response message if available
    if (body && typeof body === 'object') {
      if (body.message) {
        responseData.message = body.message;
      }
      
      // Log error details if it's an error response
      if (!responseData.success && body.error) {
        responseData.error = {
          message: body.error.message || body.message,
          stack: body.error.stack,
          code: body.error.code,
        };
      }
    }
    
    // Log response
    if (req.logConfig?.enableDetailedLogging) {
      logger.logApiResponse(responseData);
    } else {
      const level = responseData.success ? 'info' : 'error';
      logger[level](`${req.method} ${req.originalUrl || req.url} - ${res.statusCode} - ${responseTime}ms`, {
        statusCode: res.statusCode,
        responseTime,
      }, requestId);
    }
    
    return originalJson.call(this, body);
  };
  
  // Override res.status to capture status changes
  const originalStatus = res.status;
  res.status = function(code: number) {
    return originalStatus.call(this, code);
  };
  
  next();
};

// Error response logging middleware
export const logErrorResponse = (
  error: Error, 
  req: LoggedRequest, 
  res: LoggedResponse, 
  next: NextFunction
): void => {
  const requestId = req.requestId || generateRequestId();
  const responseTime = Date.now() - (req.startTime || Date.now());
  
  const responseData: ApiResponseLog = {
    requestId,
    statusCode: res.statusCode || 500,
    responseTime,
    success: false,
    timestamp: new Date().toISOString(),
    error: {
      message: error.message,
      stack: error.stack,
      name: error.name,
    },
  };
  
  logger.logApiResponse(responseData);
  next(error);
};

export default requestLogger;
