# Logger Utility Documentation

## Overview

This comprehensive logger utility provides structured logging for the RiteFit.AI backend application with support for API request/response logging, unique request tracking, and configurable detailed logging.

## Features

- ✅ **Standard Information Logs** - Support for info, warn, error, and debug levels
- ✅ **API Request Logging** - Automatic logging of headers, URL, body, and query parameters
- ✅ **Unique Request IDs** - Each request gets a unique identifier for tracking
- ✅ **Configurable Detailed Logging** - Enable/disable detailed logging per request
- ✅ **Timestamps** - All logs include precise timestamps
- ✅ **File Rotation** - Automatic log file rotation to prevent disk space issues
- ✅ **Sensitive Data Protection** - Automatic redaction of passwords, tokens, etc.
- ✅ **Multiple Output Formats** - Console and file logging with different formats
- ✅ **Error Stack Traces** - Full error tracking with stack traces

## Installation

The logger utility uses Winston and is already installed with the required dependencies:

```bash
npm install winston winston-daily-rotate-file
```

## Configuration

The logger is configured through environment variables in your `.env` file:

```env
LOG_LEVEL=info          # Logging level: error, warn, info, debug
NODE_ENV=development    # Environment affects log format and output
```

## Usage

### Basic Logging

```typescript
import { logger } from '@/utils/logger';

// Standard logging
logger.info('User logged in successfully');
logger.warn('Rate limit approaching');
logger.error('Database connection failed', new Error('Connection timeout'));
logger.debug('Processing user data', { userId: 123 });
```

### API Request Logging

The logger automatically captures API requests when the middleware is enabled:

```typescript
// Basic request logging (automatic)
GET /api/users
// Logs: "GET /api/users" with basic info

// Detailed request logging (add query parameter)
GET /api/users?detailed_logging=true
// Logs: Full request details including headers, body, query params
```

### Request ID Tracking

Every request gets a unique ID that's included in all related logs:

```typescript
// In your controller
export const getUserController = (req: LoggedRequest, res: LoggedResponse) => {
  const requestId = req.requestId!;
  
  logger.info('Fetching user data', { userId: req.params.id }, requestId);
  
  // All logs for this request will include the same requestId
  logger.debug('Database query executed', { query: 'SELECT * FROM users' }, requestId);
  
  ResponseUtil.success(res, 'User fetched successfully', { data: userData });
};
```

### Response Logging

Use the enhanced ResponseUtil for automatic response logging:

```typescript
import { ResponseUtil } from '@/utils/response';

// Success response (automatically logged)
ResponseUtil.success(res, 'Operation completed', { data: result });

// Error response (automatically logged)
ResponseUtil.error(res, 'Validation failed', 400);
```

## API Endpoints for Testing

The logger includes example endpoints for testing:

### Basic Logging Example
```
GET /api/examples/info
```

### Detailed Logging Example
```
GET /api/examples/detailed?detailed_logging=true
```

### Error Logging Example
```
GET /api/examples/error
```

### Warning Logging Example
```
GET /api/examples/warning
```

### POST Request Example
```
POST /api/examples/post-example
Content-Type: application/json

{
  "name": "Test User",
  "email": "<EMAIL>"
}
```

## Log Files

Logs are automatically written to the `logs/` directory:

- `combined-YYYY-MM-DD.log` - All logs
- `error-YYYY-MM-DD.log` - Error logs only
- `api-requests-YYYY-MM-DD.log` - API request/response logs

## Log Formats

### Console Output (Development)
```
14:30:25 info [req_1234567890_abc123]: User logged in successfully {"userId": 123}
```

### File Output (Production)
```json
{
  "timestamp": "2024-01-15 14:30:25.123",
  "level": "info",
  "message": "User logged in successfully",
  "requestId": "req_1234567890_abc123",
  "userId": 123
}
```

## Security Features

### Automatic Data Redaction

Sensitive fields are automatically redacted from logs:

**Headers:** `authorization`, `cookie`, `x-api-key`
**Body Fields:** `password`, `token`, `secret`

```typescript
// Original request
{
  "email": "<EMAIL>",
  "password": "secretpassword"
}

// Logged as
{
  "email": "<EMAIL>",
  "password": "[REDACTED]"
}
```

## Middleware Integration

The logger middleware is automatically applied to all routes:

```typescript
// In app.ts
app.use(requestLogger); // Applied early in middleware chain
```

## Error Handling Integration

Errors are automatically logged with full context:

```typescript
// Global error handler logs all unhandled errors
app.use(globalErrorHandler);

// 404 errors are also logged
app.all('*', notFoundHandler);
```

## Configuration Options

### Request Logging Configuration

```typescript
interface RequestLogConfig {
  enableDetailedLogging?: boolean;  // Enable detailed logging
  logHeaders?: boolean;             // Log request headers
  logBody?: boolean;                // Log request body
  logQuery?: boolean;               // Log query parameters
  logParams?: boolean;              // Log route parameters
  excludeHeaders?: string[];        // Headers to redact
  excludeBodyFields?: string[];     // Body fields to redact
}
```

### Logger Configuration

```typescript
interface LoggerConfig {
  level: LogLevel;                  // Minimum log level
  enableFileLogging: boolean;       // Enable file output
  enableConsoleLogging: boolean;    // Enable console output
  logDirectory: string;             // Log file directory
  maxFileSize: string;              // Max file size before rotation
  maxFiles: string;                 // Number of files to keep
  datePattern: string;              // Date pattern for file names
}
```

## Best Practices

1. **Use Request IDs**: Always pass the request ID to logger methods in controllers
2. **Appropriate Log Levels**: Use appropriate log levels (debug for development, info for important events, warn for issues, error for failures)
3. **Structured Metadata**: Include relevant metadata objects for better searchability
4. **Sensitive Data**: Never log sensitive information directly
5. **Performance**: Use debug level for verbose logging that might impact performance

## Troubleshooting

### Common Issues

1. **Logs not appearing**: Check LOG_LEVEL environment variable
2. **File permission errors**: Ensure the application has write access to the logs directory
3. **Large log files**: Log rotation is automatic, but check disk space regularly

### Debug Mode

Enable debug logging for troubleshooting:

```env
LOG_LEVEL=debug
```

This will show all log levels including detailed debug information.
